{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-76:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-eu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "63,64,65,66,67,68,69,70,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4906,5010,5182,5306,5415,5567,5692,5816,6067,6245,6353,6516,6644,6798,6958,7024,7089", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "5005,5177,5301,5410,5562,5687,5811,5919,6240,6348,6511,6639,6793,6953,7019,7084,7176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-eu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,244,292,365,435,509,633,697,847,951,1083,1135,1185,1303,1394,1434,1528,1562,1598,1654,1745,1790", "endColumns": "44,47,72,69,73,123,63,149,103,131,51,49,117,90,39,93,33,35,55,90,44,55", "endOffsets": "243,291,364,434,508,632,696,846,950,1082,1134,1184,1302,1393,1433,1527,1561,1597,1653,1744,1789,1845"}, "to": {"startLines": "190,191,192,195,196,197,198,199,200,201,202,203,204,205,209,210,211,212,213,214,215,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15898,15947,15999,16249,16323,16401,16529,16597,16751,16859,16995,17051,17105,17227,17583,17627,17725,17763,17803,17863,17958,18942", "endColumns": "48,51,76,73,77,127,67,153,107,135,55,53,121,94,43,97,37,39,59,94,48,59", "endOffsets": "15942,15994,16071,16318,16396,16524,16592,16746,16854,16990,17046,17100,17222,17317,17622,17720,17758,17798,17858,17953,18002,18997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "225,226", "startColumns": "4,4", "startOffsets": "18767,18853", "endColumns": "85,88", "endOffsets": "18848,18937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,253,325,402,476,587,685", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "119,182,248,320,397,471,582,680,748"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9742,9811,9874,9940,10012,10089,10163,10274,10372", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "9806,9869,9935,10007,10084,10158,10269,10367,10435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "882,991,1089,1199,1285,1391,1515,1601,1682,1774,1868,1964,2058,2159,2253,2349,2446,2538,2631,2713,2822,2931,3030,3139,3246,3357,3528,18007", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "986,1084,1194,1280,1386,1510,1596,1677,1769,1863,1959,2053,2154,2248,2344,2441,2533,2626,2708,2817,2926,3025,3134,3241,3352,3523,3622,18085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,310,408,523,606,673,772,840,901,989,1055,1119,1190,1253,1307,1416,1475,1538,1592,1666,1791,1881,1961,2076,2159,2241,2332,2399,2465,2536,2616,2702,2780,2858,2931,3006,3093,3180,3271,3364,3436,3512,3604,3655,3721,3805,3891,3953,4017,4080,4187,4292,4388,4494", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,97,114,82,66,98,67,60,87,65,63,70,62,53,108,58,62,53,73,124,89,79,114,82,81,90,66,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,82", "endOffsets": "225,305,403,518,601,668,767,835,896,984,1050,1114,1185,1248,1302,1411,1470,1533,1587,1661,1786,1876,1956,2071,2154,2236,2327,2394,2460,2531,2611,2697,2775,2853,2926,3001,3088,3175,3266,3359,3431,3507,3599,3650,3716,3800,3886,3948,4012,4075,4182,4287,4383,4489,4572"}, "to": {"startLines": "19,50,58,59,60,85,137,141,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "707,3627,4438,4536,4651,7578,11594,12013,12244,12305,12393,12459,12523,12594,12657,12711,12820,12879,12942,12996,13070,13195,13285,13365,13480,13563,13645,13736,13803,13869,13940,14020,14106,14184,14262,14335,14410,14497,14584,14675,14768,14840,14916,15008,15059,15125,15209,15295,15357,15421,15484,15591,15696,15792,17322", "endLines": "22,50,58,59,60,85,137,141,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,206", "endColumns": "12,79,97,114,82,66,98,67,60,87,65,63,70,62,53,108,58,62,53,73,124,89,79,114,82,81,90,66,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,82", "endOffsets": "877,3702,4531,4646,4729,7640,11688,12076,12300,12388,12454,12518,12589,12652,12706,12815,12874,12937,12991,13065,13190,13280,13360,13475,13558,13640,13731,13798,13864,13935,14015,14101,14179,14257,14330,14405,14492,14579,14670,14763,14835,14911,15003,15054,15120,15204,15290,15352,15416,15479,15586,15691,15787,15893,17400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,998,1088,1162,1239,1321,1399,1476,1544", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,993,1083,1157,1234,1316,1394,1471,1539,1659"}, "to": {"startLines": "61,62,82,83,84,142,143,193,194,207,208,217,218,219,220,222,223,224", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4734,4825,7281,7382,7486,12081,12157,16076,16165,17405,17493,18090,18164,18241,18323,18502,18579,18647", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "4820,4901,7377,7481,7573,12152,12239,16160,16244,17488,17578,18159,18236,18318,18396,18574,18642,18762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-eu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "71", "startColumns": "4", "startOffsets": "5924", "endColumns": "142", "endOffsets": "6062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "51,52,53,54,55,56,57,221", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3707,3805,3908,4008,4111,4216,4319,18401", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3800,3903,4003,4106,4211,4314,4433,18497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,104", "endOffsets": "150,252,365,470"}, "to": {"startLines": "81,138,139,140", "startColumns": "4,4,4,4", "startOffsets": "7181,11693,11795,11908", "endColumns": "99,101,112,104", "endOffsets": "7276,11790,11903,12008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,657,741,827,902,1001,1092,1187,1255,1351,1447,1514,1586,1651,1722,1849,1971,2094,2163,2252,2324,2419,2519,2621,2687,2754,2807,2865,2914,2975,3037,3109,3173,3240,3305,3369,3436,3502,3569,3623,3690,3771,3852", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,126,121,122,68,88,71,94,99,101,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "281,470,652,736,822,897,996,1087,1182,1250,1346,1442,1509,1581,1646,1717,1844,1966,2089,2158,2247,2319,2414,2514,2616,2682,2749,2802,2860,2909,2970,3032,3104,3168,3235,3300,3364,3431,3497,3564,3618,3685,3766,3847,3903"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,525,7645,7729,7815,7890,7989,8080,8175,8243,8339,8435,8502,8574,8639,8710,8837,8959,9082,9151,9240,9312,9407,9507,9609,9675,10440,10493,10551,10600,10661,10723,10795,10859,10926,10991,11055,11122,11188,11255,11309,11376,11457,11538", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,126,121,122,68,88,71,94,99,101,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "331,520,702,7724,7810,7885,7984,8075,8170,8238,8334,8430,8497,8569,8634,8705,8832,8954,9077,9146,9235,9307,9402,9502,9604,9670,9737,10488,10546,10595,10656,10718,10790,10854,10921,10986,11050,11117,11183,11250,11304,11371,11452,11533,11589"}}]}]}