{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-76:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,689,776,864,944,1031,1117,1188,1255,1353,1446,1516,1580,1642,1711,1826,1940,2053,2125,2207,2281,2347,2434,2522,2585,2650,2703,2761,2809,2870,2935,3007,3072,3140,3198,3256,3322,3386,3452,3504,3563,3636,3709", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "281,495,684,771,859,939,1026,1112,1183,1250,1348,1441,1511,1575,1637,1706,1821,1935,2048,2120,2202,2276,2342,2429,2517,2580,2645,2698,2756,2804,2865,2930,3002,3067,3135,3193,3251,3317,3381,3447,3499,3558,3631,3704,3759"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,550,7678,7765,7853,7933,8020,8106,8177,8244,8342,8435,8505,8569,8631,8700,8815,8929,9042,9114,9196,9270,9336,9423,9511,9574,10291,10344,10402,10450,10511,10576,10648,10713,10781,10839,10897,10963,11027,11093,11145,11204,11277,11350", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "331,545,734,7760,7848,7928,8015,8101,8172,8239,8337,8430,8500,8564,8626,8695,8810,8924,9037,9109,9191,9265,9331,9418,9506,9569,9634,10339,10397,10445,10506,10571,10643,10708,10776,10834,10892,10958,11022,11088,11140,11199,11272,11345,11400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,352,436,505,575,652,730,813,892,964,1043,1122,1196,1280,1364,1443,1513,1583,1665,1740,1816,1888", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "123,206,278,347,431,500,570,647,725,808,887,959,1038,1117,1191,1275,1359,1438,1508,1578,1660,1735,1811,1883,1957"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,216,217,220,228,231,232,233,235,236,238,240,241,243,246,248,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3585,4946,11808,11946,12015,12329,13267,13337,13414,17589,17672,17910,18380,18611,18690,18764,18923,19007,19157,19298,19368,19551,19777,19973,20045", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "3653,5024,11875,12010,12094,12393,13332,13409,13487,17667,17746,17977,18454,18685,18759,18843,19002,19081,19222,19363,19445,19621,19848,20040,20114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6023", "endColumns": "142", "endOffsets": "6161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7225,11493,11595,11708", "endColumns": "104,101,112,99", "endOffsets": "7325,11590,11703,11803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9639,9708,9768,9834,9901,9976,10046,10135,10219", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "9703,9763,9829,9896,9971,10041,10130,10214,10286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,993,1074,1145,1220,1291,1362,1443,1513", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,74,70,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,988,1069,1140,1215,1286,1357,1438,1508,1628"}, "to": {"startLines": "62,63,84,85,86,147,148,202,203,218,219,230,234,237,239,244,245,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4763,4859,7330,7427,7528,12099,12175,16309,16399,17751,17829,18540,18848,19086,19227,19626,19707,19853", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,74,70,70,80,69,119", "endOffsets": "4854,4941,7422,7523,7609,12170,12261,16394,16480,17824,17905,18606,18918,19152,19293,19702,19772,19968"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,84", "endOffsets": "137,222"}, "to": {"startLines": "250,251", "startColumns": "4,4", "startOffsets": "20119,20206", "endColumns": "86,84", "endOffsets": "20201,20286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "913,1021,1117,1223,1308,1411,1529,1606,1682,1773,1866,1961,2055,2154,2247,2342,2441,2536,2630,2711,2818,2923,3020,3128,3231,3333,3487,18459", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "1016,1112,1218,1303,1406,1524,1601,1677,1768,1861,1956,2050,2149,2242,2337,2436,2531,2625,2706,2813,2918,3015,3123,3226,3328,3482,3580,18535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "52,53,54,55,56,57,58,242", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3736,3834,3936,4034,4132,4239,4348,19450", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3829,3931,4029,4127,4234,4343,4463,19546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5029,5136,5292,5418,5528,5678,5800,5921,6166,6332,6440,6597,6724,6863,7017,7083,7146", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "5131,5287,5413,5523,5673,5795,5916,6018,6327,6435,6592,6719,6858,7012,7078,7141,7220"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,246,294,349,415,491,594,657,776,884,996,1046,1105,1239,1332,1371,1453,1488,1522,1574,1658,1702", "endColumns": "46,47,54,65,75,102,62,118,107,111,49,58,133,92,38,81,34,33,51,83,43,55", "endOffsets": "245,293,348,414,490,593,656,775,883,995,1045,1104,1238,1331,1370,1452,1487,1521,1573,1657,1701,1757"}, "to": {"startLines": "199,200,201,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16147,16198,16250,16485,16555,16635,16742,16809,16932,17044,17160,17214,17277,17415,17982,18025,18111,18150,18188,18244,18332,20291", "endColumns": "50,51,58,69,79,106,66,122,111,115,53,62,137,96,42,85,38,37,55,87,47,59", "endOffsets": "16193,16245,16304,16550,16630,16737,16804,16927,17039,17155,17209,17272,17410,17507,18020,18106,18145,18183,18239,18327,18375,20346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,307,407,521,602,666,754,820,883,969,1030,1088,1154,1217,1272,1390,1447,1509,1564,1633,1752,1840,1923,2032,2115,2196,2283,2350,2416,2485,2561,2647,2721,2800,2873,2944,3031,3102,3191,3281,3353,3428,3515,3566,3633,3714,3798,3860,3924,3987,4091,4200,4296,4407", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,99,113,80,63,87,65,62,85,60,57,65,62,54,117,56,61,54,68,118,87,82,108,82,80,86,66,65,68,75,85,73,78,72,70,86,70,88,89,71,74,86,50,66,80,83,61,63,62,103,108,95,110,76", "endOffsets": "224,302,402,516,597,661,749,815,878,964,1025,1083,1149,1212,1267,1385,1442,1504,1559,1628,1747,1835,1918,2027,2110,2191,2278,2345,2411,2480,2556,2642,2716,2795,2868,2939,3026,3097,3186,3276,3348,3423,3510,3561,3628,3709,3793,3855,3919,3982,4086,4195,4291,4402,4479"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "739,3658,4468,4568,4682,7614,11405,11880,12266,12398,12484,12545,12603,12669,12732,12787,12905,12962,13024,13079,13148,13492,13580,13663,13772,13855,13936,14023,14090,14156,14225,14301,14387,14461,14540,14613,14684,14771,14842,14931,15021,15093,15168,15255,15306,15373,15454,15538,15600,15664,15727,15831,15940,16036,17512", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "endColumns": "12,77,99,113,80,63,87,65,62,85,60,57,65,62,54,117,56,61,54,68,118,87,82,108,82,80,86,66,65,68,75,85,73,78,72,70,86,70,88,89,71,74,86,50,66,80,83,61,63,62,103,108,95,110,76", "endOffsets": "908,3731,4563,4677,4758,7673,11488,11941,12324,12479,12540,12598,12664,12727,12782,12900,12957,13019,13074,13143,13262,13575,13658,13767,13850,13931,14018,14085,14151,14220,14296,14382,14456,14535,14608,14679,14766,14837,14926,15016,15088,15163,15250,15301,15368,15449,15533,15595,15659,15722,15826,15935,16031,16142,17584"}}]}]}