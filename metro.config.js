// metro.config.js
const { getDefaultConfig } = require('@expo/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

// Add custom configuration to handle directory issues
defaultConfig.resolver = {
  ...defaultConfig.resolver,
  // Exclude problematic directories or files
  blacklistRE: /node_modules\/.*\/node_modules\/react-native\/.*/,
  // Ensure we're not trying to read directories as files
  dirExists: (dir) => true
};

// Add watchFolders configuration to properly handle project structure
defaultConfig.watchFolders = [__dirname];

module.exports = defaultConfig;
