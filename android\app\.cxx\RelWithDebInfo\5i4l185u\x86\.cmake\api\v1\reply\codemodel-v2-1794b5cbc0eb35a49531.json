{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-RelWithDebInfo-9b5bd90d43340dad8057.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "rnclipboard_autolinked_build", "jsonFile": "directory-rnclipboard_autolinked_build-RelWithDebInfo-84d778f37b2366bf78a9.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "RNCSlider_autolinked_build", "jsonFile": "directory-RNCSlider_autolinked_build-RelWithDebInfo-27bc1c829e932eaf5a4b.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni", "targetIndexes": [1]}, {"build": "rnpicker_autolinked_build", "jsonFile": "directory-rnpicker_autolinked_build-RelWithDebInfo-31294ad90ca79e3db0b1.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni", "targetIndexes": [10]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-RelWithDebInfo-ab5b31fa4b83d2e5e632.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [8]}, {"build": "RNGoogleMobileAdsSpec_autolinked_build", "jsonFile": "directory-RNGoogleMobileAdsSpec_autolinked_build-RelWithDebInfo-724c212858b0c8931c95.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "RNHapticFeedbackSpec_autolinked_build", "jsonFile": "directory-RNHapticFeedbackSpec_autolinked_build-RelWithDebInfo-e1872940ef2732c119c3.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "rnpdf_autolinked_build", "jsonFile": "directory-rnpdf_autolinked_build-RelWithDebInfo-363d9ae8eebf34fa06c8.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni", "targetIndexes": [9]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-RelWithDebInfo-c58356fcb48cdc33060e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [11]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-RelWithDebInfo-f0c8acdfab83fea40178.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [13]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-RelWithDebInfo-8e2ff1ecadd71a0b8293.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [12]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-RelWithDebInfo-9e9f4af398c7b31161c0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-RelWithDebInfo-65c524674595cfebff41.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-RelWithDebInfo-944dd5e5bab8f09517c7.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_RNCSlider::@4898bc4726ecf1751b6a", "jsonFile": "target-react_codegen_RNCSlider-RelWithDebInfo-9ad93af8a865740eb997.json", "name": "react_codegen_RNCSlider", "projectIndex": 0}, {"directoryIndex": 13, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-RelWithDebInfo-2ba76a67093c21d490d3.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c", "jsonFile": "target-react_codegen_RNGoogleMobileAdsSpec-RelWithDebInfo-aac58de330f6b2851ad5.json", "name": "react_codegen_RNGoogleMobileAdsSpec", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c", "jsonFile": "target-react_codegen_RNHapticFeedbackSpec-RelWithDebInfo-a93e865302cd458b5ac5.json", "name": "react_codegen_RNHapticFeedbackSpec", "projectIndex": 0}, {"directoryIndex": 12, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-RelWithDebInfo-fc50061b13cb2592b336.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-RelWithDebInfo-a67e4f90750f8882560c.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rnclipboard::@6385240493dfcaf22ab7", "jsonFile": "target-react_codegen_rnclipboard-RelWithDebInfo-abf9f6400680cf42455b.json", "name": "react_codegen_rnclipboard", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-RelWithDebInfo-d489fbc14d86c0f3fb4b.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_rnpdf::@7ef4ba29a735fbf48be7", "jsonFile": "target-react_codegen_rnpdf-RelWithDebInfo-7889092a4c192d113024.json", "name": "react_codegen_rnpdf", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516", "jsonFile": "target-react_codegen_rnpicker-RelWithDebInfo-46e4e14bd9d370b59370.json", "name": "react_codegen_rnpicker", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-RelWithDebInfo-31b5f4103a5b813a2936.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 11, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-RelWithDebInfo-06761ec60dac7bbc6102.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-RelWithDebInfo-680c1f55ae468858846b.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86", "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}