# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.25"
  }
  digests {
    sha256: "\373Ss\335v\033N\223\343\3658\305\350S\273\243\212q\024:\030\0256\350\361\223\355nN\335\263\270"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "react-android"
    version: "0.76.9"
  }
  digests {
    sha256: "\266\205?\351\n>\336LY\031m\320_.Zf\266g~\'?C=d\177\201uB5\023\326/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.7.2"
  }
  digests {
    sha256: "*\032\277\216\nY\215$k\036\334\267\036#\225\343\23723!\225\353\345\352@\243\335!5[\243\256"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.4"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.4"
  }
  digests {
    sha256: "S\232CB\215\370\2437b/\267\201@\037_\f\334\325-\2136\025\360\bZ\365\034\220\000/\3633"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.4"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.1"
  }
  digests {
    sha256: ",\'\336\031\2255gP\005U0fYzK \372\036\352|\"\212\264\357k2\265\3769\312\037Y"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.3"
  }
  digests {
    sha256: "\324\256N\215\300\312be\266\203\327\2623:\020*>\350J\331|\313\237\276\3043O\\\320\343\365M"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\347a\377oO\a\t5X\267\3157\t(\363\035S\264n\251\024A\365-\3255\332\226\324p\214+"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.3"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.3"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\363\324\365\336\0349\033\274\302\017;45\314\272\300\023R\036v\266\220-}Yc^\301\\\037y~"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.8.1"
  }
  digests {
    sha256: "\2414\332\317Nex\262\2332\351z\245\005H\320\234\264^<\263U\034\347z\302~U\342e\330\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.8.1"
  }
  digests {
    sha256: "\371\f,v\312\262\373=\270\233\370\247B\273O6\313\244>RG*\357\363\221\236\221C\275^\300r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.1"
  }
  digests {
    sha256: "^\353Yd\250\355\262Yf\325\314y3\376\211><X\342E\254C=5\204x\262\vm\t\324\343"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.1"
  }
  digests {
    sha256: "\031\272P\320\224\3076\216\336\033L\317\021\225\316\270>5\227\a6Y?\202>Z\367\026\370\320]p"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.3"
  }
  digests {
    sha256: "\001w\226\224\261\350\"\322\356\271\336\316\310o@\334)\203\200k\230_\264\216\341\026\310`SNz\305"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.8.3"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.8.3"
  }
  digests {
    sha256: "\375\277pb\vHU\n\262?\317\004\331\356Y\022;\370\303\035\262`\026\312t\3233*yDJ\356"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.8.3"
  }
  digests {
    sha256: "\326\276\336\302X\343\277\310m,\272\'\376\244\300=\202\207 W\277\312*CBF\036m\357\262\323^"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.3"
  }
  digests {
    sha256: "\323\372\"W\315L\365\022q}\300\214\0233\210r\2616\271}-Q\017\3167;\234\303\221\352\031\271"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\303?\234\275\223\036a\220\3128\252\t\277\212z\212\0319\035K\017\267\247`ZkY\362\324%\200\321"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.8.3"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\312\316\377\320v\271\207Ou\352\307 \256H\313.\213\372Uc\023\225\271\177\260\366\202\322y4*\025"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.7.6"
  }
  digests {
    sha256: "H)Bh2t\341\226\027\'\262\247mh\376J\264\253\270\024_\330\263\024aB\024L\340\262S\021"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.7.6"
  }
  digests {
    sha256: "\200,\2300<%\n\221\270\235kp<>\253s\277\366\315\"\n\205\375\321\016\027\312\022o\225Hy"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.3"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\323\365\321B;\306\334\fsC\247#\211B[\344\266\357\203.\364a\372\361T+\274bK\3339\030"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.8.3"
  }
  digests {
    sha256: "\324\034q\221\370\352\323\021\307\367\362j\314\3170\213\245k|\373\376\273%<C\203\027P\261\232\251\310"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.3"
  }
  digests {
    sha256: "C\322\212lm\251\301\313r\277\265\314\033Q\032y7\262\333\213y\325/7\320\267\361Ly\260\220\335"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\273\365M\3140\f\201\352\221\365fz\300\270Y4\262\313\312\377\300\200m\203\211\337\024l\302\024\212\\"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.3"
  }
  digests {
    sha256: "\341{\f\324\000\201\250l\243\255\313\213\023^\350/=\330B\333B\a.\262\220\034d\242w:mu"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.3"
  }
  digests {
    sha256: "\203\235\321$S\304\340Q\026s(1\377I\362\020\211<\311\233p\221\273\232\022I\210\221\034Sw4"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.1"
  }
  digests {
    sha256: ">E\225\315\251\276\343\222qYY6mpy\a\004\377$\fwenp/<\202 \263\331$Z"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.7.2"
  }
  digests {
    sha256: "\260\264 n\316\222\221\231%\006\037\337W\204\335!\360\021\2054`\236\217m\224\004\275\320\365\313Z="
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.1.0"
  }
  digests {
    sha256: "g\316\270&|\'EW\303WX\353\276D\211\n :Uy\275+\bM\363Mpv<r\370\017"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.facebook.fbjni"
    artifactId: "fbjni"
    version: "0.6.0"
  }
  digests {
    sha256: "\350qe\323\300\370\023q\372\003\372|\256P\356\025v\305P2\225\026\273\037Ix\364\031\264\260}L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "nativeloader"
    version: "0.12.1"
  }
  digests {
    sha256: "\227\035\355\206\000\234\n\305o\262\'^\022\217\205.}\313\304\032@\315y\226\210\370\241\256\275\345\031]"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fresco"
    version: "3.2.0"
  }
  digests {
    sha256: "\261\005o^\247\357\324\250\"\252|\310\231i.%<\344\275\256\000\346\217IZ\362\037l\351\302R\233"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "soloader"
    version: "3.2.0"
  }
  digests {
    sha256: "\037\254\275\246\336*\376`\335\376\230\r\303\253Z\034\024qf\fJs\021\354B|EI\212\306\250\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fbcore"
    version: "3.2.0"
  }
  digests {
    sha256: "\035\321\342\266y\r\027^c$\022\235;\350\234\336j\002\002\3725^%Y\214\377\253\005\\\202&B"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "soloader"
    version: "0.12.1"
  }
  digests {
    sha256: "[\306\341]q/\220\240\243\211\r\"\333\016\357_\350\236\363\374\263\256\212\241\370\366\246\331\225G\310\222"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "annotation"
    version: "0.12.1"
  }
  digests {
    sha256: "\366\335\325Rh\277\030$\"]\367\243^\301\270\263<\371\210U\246\311\301S\321\211\211\236^\364\244\030"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "ui-common"
    version: "3.2.0"
  }
  digests {
    sha256: "\300\00358\232\220\277pL\033\272\t/\347\227\365&\033!\206f\254\306N\000\272k\344!\237\200\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "middleware"
    version: "3.2.0"
  }
  digests {
    sha256: "\345\256jW~\002z\324\200g\202\215\336\245W\254M\243\001\236_r\304\334}\026\317\006\316\337Q\003"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "drawee"
    version: "3.2.0"
  }
  digests {
    sha256: "1*Tt\244\326\240\270\016\370E\300-,a\215\351\001(q\272\257\377\277\274`|62h\350\300"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline"
    version: "3.2.0"
  }
  digests {
    sha256: "\006\234w=>\325nG\266~\344\306\323\207[|\261\227\334lQS\263\r)\210\376\001\246\353\366\363"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.parse.bolts"
    artifactId: "bolts-tasks"
    version: "1.4.0"
  }
  digests {
    sha256: "\233\305\036>\312\205:\235\362\373\254b\3021}\262\311\352a\233\225\265S\0340D\002C\332\254\316\342"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-base"
    version: "3.2.0"
  }
  digests {
    sha256: "\367\333C\233.\320\023\nWu\202\3048 \336\213<\271\362.\205\310\2340\327\225\207\' \257ot"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.infer.annotation"
    artifactId: "infer-annotation"
    version: "0.18.0"
  }
  digests {
    sha256: "\312\357\272\223VC\334\226\'X\247`\361bz@\"\025\257\003M\226\300\364\204\326;\177\335\312\231/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-annotations-jvm"
    version: "1.3.72"
  }
  digests {
    sha256: ">\343\245m\324Q\343?\203R\340\322\200:U\312\033\221V\327\370\250\340u\233\370\341\207\377\215\262J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-native"
    version: "3.2.0"
  }
  digests {
    sha256: "h\351C2`T-4\337\363fl\rV\022\314\335\031\250\247,\272+\330\223Y\365o\361\350\262."
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-ashmem"
    version: "3.2.0"
  }
  digests {
    sha256: "PE\361!Y\336zn\034\244\365fT)\005\fvx\002%\223\307=\271\204\037Y\027\362\373\t\245"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-native"
    version: "3.2.0"
  }
  digests {
    sha256: "#gmW >y\261\226\241\323\232\246\372\350b\202~.\351~\3775Dh\360KxZ\307A\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-java"
    version: "3.2.0"
  }
  digests {
    sha256: "\300x\327E\336\353\277L\022\302\252\r\243z\357ewg.\'\276r\230\025f\300\212\301$\207\230\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagefilters"
    version: "3.2.0"
  }
  digests {
    sha256: "\236=\317\333\275\345i\320\371\306\276fw\376\035\224\270\223J7ks\236\232\033\311P%8m\3468"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagetranscoder"
    version: "3.2.0"
  }
  digests {
    sha256: "\344\234\256D\317\273\351\261$(\340\2643\ra\340\t\275\002\b\313\204\271\321\2364\211\354\327\025\222\033"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-okhttp3"
    version: "3.2.0"
  }
  digests {
    sha256: "\260\263\f\274\'\342o\317<\373v\321\361S\252\r\305\255\334\\[\251\241\rw\366\2142\207\023\300\350"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.9.2"
  }
  digests {
    sha256: ";.\341\267h\301\337(\303\r/\346\263\215\332M,Q\222\020\343\f\243\322yPa\214V<\222\336"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "2.9.0"
  }
  digests {
    sha256: "\271%\\\026;~\334\v\204\006U\235fW\234l2\336\240\037i\031C\272\305\323\375\275\020\366\233D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.yoga"
    artifactId: "proguard-annotations"
    version: "1.19.0"
  }
  digests {
    sha256: "\373e\367\006\356\240[V@\373k\223\322\035lA\371\342\264\347\246\346\331\362\214\351\322\245tGI\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp-urlconnection"
    version: "4.9.2"
  }
  digests {
    sha256: "\016\0349\252\211f\217\226%\311 z\315J\207\204\334\206\212\320\325\242\001\035\236\025\340gO;}\021"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-reflect"
    version: "1.9.25"
  }
  digests {
    sha256: "\017\203\206\335\375i\347\364T\241\346\271\v\'\035\314\331\001\331j%\177\337c>\317\005/\270\225\035\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.7.6"
  }
  digests {
    sha256: "\\\313\226\272\260@\247\017\177\335W\177\252\255u42a\300\006\324/\314W#\243\217P\250/Y|"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.7.6"
  }
  digests {
    sha256: "\001\363I\325L\371\354A\315\n\337]\214*\206\234,$[\215\273k\345\245~~\276=h\323\027\221"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.7.6"
  }
  digests {
    sha256: "\227\"\324\374\315J\236\225\221\v\252\026e%c\272\222\245\202\237\235n\t\031HPD~<\331\230:"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.7.6"
  }
  digests {
    sha256: "B\337\006\3439\b\b\310RV\247\357\327MD\215\247\034\274\026\213AIz0SPE\311&\365\260"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.7.6"
  }
  digests {
    sha256: "c\353=\004\232\214\201\2368\005\325\034\341[\222\003$\234\351`\354\375S%\021&Y G\274/\360"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.7.6"
  }
  digests {
    sha256: "\367A \224\237\330\370/\277\274\303y2F\020\217\225T\337\017Q\354\307\373]r\032\311q\3049J"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.7.6"
  }
  digests {
    sha256: "\000\311j.T\260;\300\267+\217\3042{\322\247[h\234h\232\240\022\225\330p\024\036\271\335\335\320"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.7.6"
  }
  digests {
    sha256: "\236\326!O\335M\242r\260\262X,B\217\214=\316\\\346\241\206\304}\252\371\374\001\256\302&\241\231"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.7.6"
  }
  digests {
    sha256: "\322)\030B\361\303\350\347\fL\265\311b\300uNF>\002?\357\220o47\302\t\240\226\300\236\307"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.7.6"
  }
  digests {
    sha256: ")n\024\342\263\177{\240\236A\205c\177q@\221\231\274\355!\325\240p\376&\366\334k&\325\210\266"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.android.installreferrer"
    artifactId: "installreferrer"
    version: "2.2"
  }
  digests {
    sha256: "q\022\345\222\304)\224\227\207\301h\305\254br\021D\247;d\220PhEW\375\275\272]\323\266j"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer"
    version: "2.18.1"
  }
  digests {
    sha256: "V\213\033\a>J\265\343\nY\0314\340;\221\'\001\354r\203\256\262)\036\002\032\235\032y\023\254\315"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-common"
    version: "2.18.1"
  }
  digests {
    sha256: ",\246x\300\310\21156\034\001\367TUt}\346\022Ee\264ab\313\274\253\000\252\305I\214\310F"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-database"
    version: "2.18.1"
  }
  digests {
    sha256: "\327Q\016\226\240\330\217\031|X\000\313\307\241\201\304Av`\f\330:\006\275.\217\372\222\340_U+"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-datasource"
    version: "2.18.1"
  }
  digests {
    sha256: "L\"\217\316J\"\377\231\'&\202\036\3559\221MJ\3252\273\274KJ\323\034\024\206\2550n\275\262"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-decoder"
    version: "2.18.1"
  }
  digests {
    sha256: "#\337$D\265\205\344`\372/#\344Bi\327_`\0362\330q\365\204\347\320{T\213\236g^f"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-extractor"
    version: "2.18.1"
  }
  digests {
    sha256: "R\232\030\260\244\023\v\207\331lP\222K\250\343\247%5et\321\027\2133\321Dy\255\251\255\000j"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-core"
    version: "2.18.1"
  }
  digests {
    sha256: "\360~\364\r\215\201f\276\267\213\005\026\313\271:\000S1\235iC\000%]TH3h\271\242\020_"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-dash"
    version: "2.18.1"
  }
  digests {
    sha256: "\357\215\350#c\216\330\000p\005\225\a5=Y\221\324ZX\036\374\261\231\346Cr\210X\241\207\324\346"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-hls"
    version: "2.18.1"
  }
  digests {
    sha256: "5\363\202\344\3607\373\330\200[\234\bJ\340\303HVf\254\342e\253\326I\b4\241:\231T0\307"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-rtsp"
    version: "2.18.1"
  }
  digests {
    sha256: "\307\"\3675\221\372\370[>V\016\005\177\320\375\016\361\250\261\221\267w\333h\212\373\'\315\300\237\240a"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-smoothstreaming"
    version: "2.18.1"
  }
  digests {
    sha256: "g O4\254Z\304t\326\270{\227\340W\372\001\211T\235\264\003\333\251\232W\337\353\333!\a\310\300"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-ui"
    version: "2.18.1"
  }
  digests {
    sha256: ".R\366$\254A\004\256\327\224`f1?\361\332:\272\026S]\370\3711\251\307\242\030\256y\254W"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.2.1"
  }
  digests {
    sha256: "\241\352\003)\356m\223\203\005\337\320\370\316\\H\336\242\252\301NV\006\322>\177\266\n\374\373e]n"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.4.3"
  }
  digests {
    sha256: "\211\250=d\215\257\353Q`\247m\315\371\226s]toA\f\021Qf\317\025i;R\b\327=\225"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "extension-okhttp"
    version: "2.18.1"
  }
  digests {
    sha256: "\022am+\304\321}\273\216\'\302B0d\322\203\003\350\305\005\002\207\202\234\313\273G\334\344I\023\361"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.6"
  }
  digests {
    sha256: "\370w\323\004f\n\302\241B\363\206[\255\374\227\035\354~\327<t|\177\215]/Q9\312se\023"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.6.0"
  }
  digests {
    sha256: "\247\355\257=s\2556M\226{\203z\201\210n\303\264\ar\354a\000+\310\202\016\034c\325\322O\222"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-extensions"
    version: "2.2.0"
  }
  digests {
    sha256: "d\214\215\341\321\v\002]RJ.F\254\231O\303\366\277\030h&\300\236\301\246-%\v\361\270w\256"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-core"
    version: "3.4.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-core-jvm"
    version: "3.4.0"
  }
  digests {
    sha256: "o\311S\217\256A\036\255\356\333\024\221\375iN\274c5r\025~\333\027o+,\237h^\"\231<"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.2.0"
  }
  digests {
    sha256: "w\224\b\261\2523\f\324\247\255\361\262\350_\322\211\303\016~\335.\216B\204{}\006X\232\367\025\372"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.6.1"
  }
  digests {
    sha256: "\202\306_Q0\252\253\303\252\002\314x\377}u\362a\354\341\236i\270\376\306\340wqr\363\346\311\215"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.1"
  }
  digests {
    sha256: "\354\025\265\324\242\357\360x\210\274\024\231\316.,n\376$\300\355`\314W\260\214\235\304\266\375<!\211"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.1"
  }
  digests {
    sha256: "\26272\355\2735\021\3317\376\241\377\357\004{\016l\000\033P\301\222\037\r\225\237\303\204\327\006\354j"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.8.6"
  }
  digests {
    sha256: "\310\373H9\005M(\v03\370\000\321\365\251}\342\360(\353\213\242\353E\212\322\207\3456\363\362_"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.device.yearclass"
    artifactId: "yearclass"
    version: "2.1.0"
  }
  digests {
    sha256: "\353\030\227*\vy\265\320\020\205\214\256\335\323\327\3566\212\231\202g\211g\030\004G\300c\202\352qs"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.0.0"
  }
  digests {
    sha256: "v\277\373|\357\277x\a\224\330\201p\002\332\321V/>\'\300\251\367F\326$\001\310\355\263\n\356\336"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.10"
  }
  digests {
    sha256: "BA\337\251Nq\035C_)\244`J>-\345\304\252<\026^#\275\006k\346\374\037\3040\225i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.16.0"
  }
  digests {
    sha256: "\211\201\034c\335&jHQ\375\033y\306\374\f\230)\226\354\3445\365\377H?\2700\312l\265<\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.16.0"
  }
  digests {
    sha256: "\225_\207*\364\322\243!\376\242\243F\374G\"%\242\271\225$\254\304g\201I\027\022\343\233j!O"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.16.0"
  }
  digests {
    sha256: "\242\'\366U\234\020J\245\245\310\213\np\351\307\343\333hY\343\vGt\363\202\221!\337\236bst"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.16.0"
  }
  digests {
    sha256: "\305\2369\261\263\033MU\222\'|\331\000\022\024\235t\vV\235C8\365dC\312\310,?\275\243\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.github.CanHub"
    artifactId: "Android-Image-Cropper"
    version: "4.3.1"
  }
  digests {
    sha256: "\324\212r&\354\231\223j\347wo\267\2145\251\246\202\264\366\264WI\242U\233_\031@\233\f\206\343"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.25"
  }
  digests {
    sha256: "\232MzW\n\221\322\371\a{\333\334\253KP\246\215\243:\264I\351\202\324nf\376\361\361L\004\224"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.25"
  }
  digests {
    sha256: "\251|\235\276A\030U\276>\200\376P\'X\331J\346\024\033\247\362z\241\257\201P\365\263D\334\371P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.0.1"
  }
  digests {
    sha256: "\022q\304\240\247\347\265\305\236\026w3 \\]R< \b\337v\374X\235\265q\217\032o\355+p"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.2.0"
  }
  digests {
    sha256: "\262\262\217k\241s\365\340\304\376=6\242\327\356R9\307\254\277AC\265I\354\3601\243H[5\273"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.9"
  }
  digests {
    sha256: "At\\[\217B}$C\220\025\270Oe\032\324 q\211\221\206}*\374&,&@\t\270\002\301"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.9"
  }
  digests {
    sha256: "\a\243 %\246[\b\356~\021\321M\3059\247X\266g\023\360\301\3219\000\250\025\231\316\255\272\364M"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.2.0"
  }
  digests {
    sha256: "\336\001\036j;ya\336c\217\027/,\266k\243\004\352\251\211f*\236+\017H\337\367t\314\301f"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.0.1"
  }
  digests {
    sha256: "(\226\327oC+\345!g)[\271\316E\255\342\\1\n\357\374\004\322\214\370\333j\025\206\216\203\336"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "me.leolin"
    artifactId: "ShortcutBadger"
    version: "1.1.22"
  }
  digests {
    sha256: "\315\026\020\334H\305i\222)P!\207\375\303\265\305C8p\325\252\261Y3!!\261\370\301\332\330\303"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-splashscreen"
    version: "1.2.0-alpha02"
  }
  digests {
    sha256: "\375\205\363|\272\310\003\363\201\213\260\264\347c\245\333\223\270\326\033\324:\270tad\333\202\'\001h\261"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "okhttp3-integration"
    version: "4.12.0"
  }
  digests {
    sha256: "\rr.\364\332\227\037=\t\352\a5\035\323\251\377s\017\261{C\213\035MtO\232(\246\315\326 "
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "hermes-android"
    version: "0.76.9"
  }
  digests {
    sha256: "\366@+yqZ\265\243rM5[\220\234\272\0069[3\372\271f\215\301C\332\'\370<\333\357\362"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.ump"
    artifactId: "user-messaging-platform"
    version: "3.1.0"
  }
  digests {
    sha256: "\255\315t\376 ?\222\vb\372\321\300e\261\351\335\200R\235\336X\023\036\356\025\325\345h\000\332[["
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads"
    version: "23.6.0"
  }
  digests {
    sha256: "\001^\346M\272\364\"\243\324\357\315+\243h\354\347W\331\221\312.!\230a\177s/\022M\n\355\237"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.11.0-alpha02"
  }
  digests {
    sha256: ";\037^#n\221\231\2518\375\vP{\306\316q\034F\315\002\206\275@\246\330r\336@\232\267\331N"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-base"
    version: "23.6.0"
  }
  digests {
    sha256: "%\277\321\262\333\327,\312t\274\223\037j\023\331O\320\027x\310\337\257}\347\207\360\262|\003\031\017 "
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-lite"
    version: "23.6.0"
  }
  digests {
    sha256: "\t\207\201\025\0161\262\367bT\001\373\025.\272\276\235\004\330r\214-\356\326\250\\\263)\264\3273#"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\210\202>\000\272\232\256m\306Cj\355Y(\300p\326\212b\302X\035\351ne\\\250o\003\016r\251"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.2.5"
  }
  digests {
    sha256: "$\245T\233ynC\3437Q=)\b\255\254g\364SP\331\251\v\312~.a i!@\273\024"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.2.5"
  }
  digests {
    sha256: "+\023\r\324\241\323\331\033g\001\3553\tm8\237\001\304\374\021\227\247\254\326\271\027$\335\305\254\374\006"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.1.0"
  }
  digests {
    sha256: "\206ss\177\333.\373\255\221\256\256\355\031\'\353\262\222\022\323j\206}\223\271c\234\200i\001\237\212\036"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.1.0"
  }
  digests {
    sha256: "\203A\377\t-``\326*\a\"\177)#qU\377\363o\261o\226\311_\275\232\210N7]\271\022"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "20.1.2"
  }
  digests {
    sha256: "\315\221\rE\276~\276;p\311\244\301l\233\356e\350t\201\025\355@\0226C\230O5\351\033_8"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "20.1.2"
  }
  digests {
    sha256: "\356\224}z\017\342,Z2\357l\233oz\020z\352j\320pDu\207N\326is\bC\034{\276"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-appset"
    version: "16.0.1"
  }
  digests {
    sha256: "\340N\315Ou\317E\312,7\273S\373 \224Z\234Z2,\025\341l$\031pmg/\260\020\016"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.github.zacharee"
    artifactId: "AndroidPdfViewer"
    version: "4.0.1"
  }
  digests {
    sha256: "de\270\376\275\277u\027(\220\254\315-\321\236E\256\253\234/\363(\274\021\275\215\262\222\247DT\334"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "io.legere"
    artifactId: "pdfiumandroid"
    version: "1.0.24"
  }
  digests {
    sha256: "\276\330\313\333m\356\243\345\341\\\212K\243~\027\\\231\221\256\3335\242\272M:5u\305\352\351\272\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-gif"
    version: "3.2.0"
  }
  digests {
    sha256: "W\363\215\200\340;\364\357\222\276D.\221\357\264\304V_\275*+\177\246\002\277[\f\312-\247l}"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-base"
    version: "3.2.0"
  }
  digests {
    sha256: "\020\323Ykl:o6@\025\f\307\320\255\311\262\357\363V\267\326\215\273\332|lR\016\307#g0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "vito-options"
    version: "3.2.0"
  }
  digests {
    sha256: "\002\334\346\341\373\344\214#:\342\220F\316S\240$\2318\212\274\321\375\276\270g\026s\2624\003\253l"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-drawable"
    version: "3.2.0"
  }
  digests {
    sha256: "\027\311\375\b\232/\251\333\363\325\370\302\350\325\240\373\235\200\356U\276\272<\036\321%{\352\371\025x\342"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "vito-renderer"
    version: "3.2.0"
  }
  digests {
    sha256: "\n!\276\367C\201\216\241\021\223\230\216\2414u\264\362+t\033\031[\337edZ\324\346\247c\306\374"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "webpsupport"
    version: "3.2.0"
  }
  digests {
    sha256: "\262\351\177\244\276\201\212`\024\273=\342+\r@[\0019b52\210\a\214\272\376\276\336\322\266g\004"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 63
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 39
  library_dep_index: 73
  library_dep_index: 75
  library_dep_index: 95
  library_dep_index: 81
  library_dep_index: 80
  library_dep_index: 86
  library_dep_index: 78
  library_dep_index: 98
  library_dep_index: 87
  library_dep_index: 99
  library_dep_index: 96
  library_dep_index: 97
  library_dep_index: 100
  library_dep_index: 0
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 63
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 66
  library_dep_index: 67
  library_dep_index: 68
  library_dep_index: 69
  library_dep_index: 31
  library_dep_index: 18
  library_dep_index: 50
  library_dep_index: 57
  library_dep_index: 70
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 63
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 18
  library_dep_index: 50
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 54
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 61
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 15
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 62
  library_dep_index: 0
  library_dep_index: 32
}
library_dependencies {
  library_index: 14
  library_dep_index: 0
}
library_dependencies {
  library_index: 15
  library_dep_index: 8
  library_dep_index: 16
}
library_dependencies {
  library_index: 17
  library_dep_index: 8
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 8
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 57
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 20
  library_dep_index: 8
}
library_dependencies {
  library_index: 21
  library_dep_index: 8
  library_dep_index: 20
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 0
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 25
}
library_dependencies {
  library_index: 27
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 0
}
library_dependencies {
  library_index: 28
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 29
  library_dep_index: 0
}
library_dependencies {
  library_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 18
  library_dep_index: 50
  library_dep_index: 53
  library_dep_index: 56
  library_dep_index: 57
  library_dep_index: 54
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 60
}
library_dependencies {
  library_index: 32
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 0
  library_dep_index: 13
}
library_dependencies {
  library_index: 33
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 34
  library_dep_index: 8
  library_dep_index: 22
  library_dep_index: 22
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 35
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 36
  library_dep_index: 33
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 37
  library_dep_index: 8
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 38
  library_dep_index: 8
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 8
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 39
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 8
  library_dep_index: 43
  library_dep_index: 18
  library_dep_index: 47
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 43
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 24
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 43
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 8
  library_dep_index: 18
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 49
  library_dep_index: 18
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 50
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 24
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 52
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 8
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 50
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
}
library_dependencies {
  library_index: 54
  library_dep_index: 8
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 0
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 54
}
library_dependencies {
  library_index: 56
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 35
  library_dep_index: 50
}
library_dependencies {
  library_index: 57
  library_dep_index: 8
  library_dep_index: 15
  library_dep_index: 38
  library_dep_index: 16
}
library_dependencies {
  library_index: 58
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 59
}
library_dependencies {
  library_index: 59
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
  library_dep_index: 12
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 36
  library_dep_index: 52
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 31
}
library_dependencies {
  library_index: 61
  library_dep_index: 7
  library_dep_index: 32
  library_dep_index: 47
  library_dep_index: 52
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 7
}
library_dependencies {
  library_index: 62
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 63
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 64
  library_dep_index: 65
  library_dep_index: 6
}
library_dependencies {
  library_index: 64
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 65
  library_dep_index: 64
  library_dep_index: 17
  library_dep_index: 10
}
library_dependencies {
  library_index: 66
  library_dep_index: 8
}
library_dependencies {
  library_index: 67
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 59
}
library_dependencies {
  library_index: 68
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 68
  library_dep_index: 68
}
library_dependencies {
  library_index: 70
  library_dep_index: 8
}
library_dependencies {
  library_index: 71
  library_dep_index: 13
}
library_dependencies {
  library_index: 72
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 17
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
}
library_dependencies {
  library_index: 75
  library_dep_index: 76
  library_dep_index: 74
  library_dep_index: 80
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 82
  library_dep_index: 83
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 4
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 77
  library_dep_index: 13
  library_dep_index: 4
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
  library_dep_index: 74
}
library_dependencies {
  library_index: 80
  library_dep_index: 77
  library_dep_index: 4
}
library_dependencies {
  library_index: 81
  library_dep_index: 77
  library_dep_index: 80
  library_dep_index: 4
}
library_dependencies {
  library_index: 82
  library_dep_index: 77
  library_dep_index: 83
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 80
  library_dep_index: 81
}
library_dependencies {
  library_index: 83
  library_dep_index: 74
  library_dep_index: 79
  library_dep_index: 84
  library_dep_index: 77
  library_dep_index: 81
  library_dep_index: 0
  library_dep_index: 85
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
  library_dep_index: 79
  library_dep_index: 84
  library_dep_index: 77
  library_dep_index: 81
  library_dep_index: 4
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
  library_dep_index: 88
}
library_dependencies {
  library_index: 89
  library_dep_index: 83
  library_dep_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 90
  library_dep_index: 77
  library_dep_index: 83
}
library_dependencies {
  library_index: 91
  library_dep_index: 77
  library_dep_index: 83
  library_dep_index: 89
  library_dep_index: 74
}
library_dependencies {
  library_index: 92
  library_dep_index: 77
  library_dep_index: 83
  library_dep_index: 89
}
library_dependencies {
  library_index: 93
  library_dep_index: 83
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 74
  library_dep_index: 84
  library_dep_index: 77
}
library_dependencies {
  library_index: 94
  library_dep_index: 85
  library_dep_index: 74
  library_dep_index: 84
  library_dep_index: 77
}
library_dependencies {
  library_index: 95
  library_dep_index: 77
  library_dep_index: 83
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 81
  library_dep_index: 96
  library_dep_index: 4
}
library_dependencies {
  library_index: 96
  library_dep_index: 97
  library_dep_index: 0
}
library_dependencies {
  library_index: 97
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 99
  library_dep_index: 96
  library_dep_index: 4
}
library_dependencies {
  library_index: 101
  library_dep_index: 0
}
library_dependencies {
  library_index: 102
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 103
  library_dep_index: 121
  library_dep_index: 43
  library_dep_index: 107
  library_dep_index: 117
  library_dep_index: 111
  library_dep_index: 13
  library_dep_index: 68
  library_dep_index: 0
  library_dep_index: 121
}
library_dependencies {
  library_index: 103
  library_dep_index: 104
}
library_dependencies {
  library_index: 104
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 105
  library_dep_index: 121
  library_dep_index: 43
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 111
  library_dep_index: 0
  library_dep_index: 105
}
library_dependencies {
  library_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 106
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 43
  library_dep_index: 107
  library_dep_index: 113
  library_dep_index: 115
  library_dep_index: 111
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 103
}
library_dependencies {
  library_index: 107
  library_dep_index: 108
}
library_dependencies {
  library_index: 108
  library_dep_index: 61
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 71
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 117
  library_dep_index: 115
  library_dep_index: 111
  library_dep_index: 13
  library_dep_index: 120
  library_dep_index: 68
  library_dep_index: 41
  library_dep_index: 50
  library_dep_index: 57
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 24
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 117
  library_dep_index: 115
  library_dep_index: 111
}
library_dependencies {
  library_index: 109
  library_dep_index: 110
}
library_dependencies {
  library_index: 110
  library_dep_index: 8
  library_dep_index: 43
  library_dep_index: 111
  library_dep_index: 0
  library_dep_index: 107
  library_dep_index: 113
  library_dep_index: 117
  library_dep_index: 115
  library_dep_index: 111
}
library_dependencies {
  library_index: 111
  library_dep_index: 112
}
library_dependencies {
  library_index: 112
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 117
  library_dep_index: 115
}
library_dependencies {
  library_index: 113
  library_dep_index: 114
}
library_dependencies {
  library_index: 114
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 43
  library_dep_index: 115
  library_dep_index: 111
  library_dep_index: 13
  library_dep_index: 119
  library_dep_index: 0
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 117
  library_dep_index: 115
  library_dep_index: 111
}
library_dependencies {
  library_index: 115
  library_dep_index: 116
}
library_dependencies {
  library_index: 116
  library_dep_index: 8
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 43
  library_dep_index: 109
  library_dep_index: 111
  library_dep_index: 0
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 117
  library_dep_index: 111
}
library_dependencies {
  library_index: 117
  library_dep_index: 118
}
library_dependencies {
  library_index: 118
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 113
  library_dep_index: 115
  library_dep_index: 111
  library_dep_index: 13
  library_dep_index: 68
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 115
  library_dep_index: 111
}
library_dependencies {
  library_index: 119
  library_dep_index: 13
  library_dep_index: 0
}
library_dependencies {
  library_index: 120
  library_dep_index: 32
  library_dep_index: 0
}
library_dependencies {
  library_index: 121
  library_dep_index: 122
}
library_dependencies {
  library_index: 122
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 105
  library_dep_index: 43
  library_dep_index: 107
  library_dep_index: 115
  library_dep_index: 111
  library_dep_index: 13
  library_dep_index: 0
}
library_dependencies {
  library_index: 124
  library_dep_index: 125
  library_dep_index: 131
  library_dep_index: 132
  library_dep_index: 133
  library_dep_index: 134
  library_dep_index: 135
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 140
}
library_dependencies {
  library_index: 125
  library_dep_index: 8
  library_dep_index: 126
}
library_dependencies {
  library_index: 126
  library_dep_index: 127
  library_dep_index: 16
  library_dep_index: 87
  library_dep_index: 128
  library_dep_index: 129
  library_dep_index: 130
}
library_dependencies {
  library_index: 131
  library_dep_index: 125
  library_dep_index: 8
}
library_dependencies {
  library_index: 132
  library_dep_index: 125
  library_dep_index: 131
  library_dep_index: 8
}
library_dependencies {
  library_index: 133
  library_dep_index: 125
  library_dep_index: 8
}
library_dependencies {
  library_index: 134
  library_dep_index: 8
  library_dep_index: 125
  library_dep_index: 133
}
library_dependencies {
  library_index: 135
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 125
  library_dep_index: 132
  library_dep_index: 133
  library_dep_index: 134
  library_dep_index: 131
}
library_dependencies {
  library_index: 136
  library_dep_index: 135
  library_dep_index: 8
}
library_dependencies {
  library_index: 137
  library_dep_index: 8
  library_dep_index: 135
}
library_dependencies {
  library_index: 138
  library_dep_index: 8
  library_dep_index: 135
}
library_dependencies {
  library_index: 139
  library_dep_index: 135
  library_dep_index: 8
}
library_dependencies {
  library_index: 140
  library_dep_index: 125
  library_dep_index: 8
  library_dep_index: 141
  library_dep_index: 142
}
library_dependencies {
  library_index: 141
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 59
  library_dep_index: 10
}
library_dependencies {
  library_index: 142
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
}
library_dependencies {
  library_index: 143
  library_dep_index: 125
  library_dep_index: 132
  library_dep_index: 8
  library_dep_index: 96
}
library_dependencies {
  library_index: 145
  library_dep_index: 8
}
library_dependencies {
  library_index: 146
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 31
  library_dep_index: 22
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 49
  library_dep_index: 50
}
library_dependencies {
  library_index: 147
  library_dep_index: 148
}
library_dependencies {
  library_index: 148
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 149
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 15
  library_dep_index: 13
  library_dep_index: 17
  library_dep_index: 16
}
library_dependencies {
  library_index: 150
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 59
}
library_dependencies {
  library_index: 151
  library_dep_index: 8
  library_dep_index: 6
  library_dep_index: 152
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 13
  library_dep_index: 67
  library_dep_index: 155
  library_dep_index: 14
  library_dep_index: 31
  library_dep_index: 18
  library_dep_index: 141
  library_dep_index: 160
  library_dep_index: 64
  library_dep_index: 161
}
library_dependencies {
  library_index: 152
  library_dep_index: 8
}
library_dependencies {
  library_index: 153
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 154
}
library_dependencies {
  library_index: 155
  library_dep_index: 13
  library_dep_index: 10
  library_dep_index: 156
}
library_dependencies {
  library_index: 156
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 157
  library_dep_index: 56
  library_dep_index: 158
  library_dep_index: 159
}
library_dependencies {
  library_index: 157
  library_dep_index: 8
}
library_dependencies {
  library_index: 158
  library_dep_index: 8
}
library_dependencies {
  library_index: 159
  library_dep_index: 8
}
library_dependencies {
  library_index: 160
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 161
  library_dep_index: 8
  library_dep_index: 31
  library_dep_index: 141
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 164
  library_dep_index: 13
  library_dep_index: 142
  library_dep_index: 156
  library_dep_index: 165
  library_dep_index: 31
}
library_dependencies {
  library_index: 165
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 156
  library_dep_index: 59
  library_dep_index: 58
  library_dep_index: 150
  library_dep_index: 67
  library_dep_index: 166
  library_dep_index: 17
  library_dep_index: 72
  library_dep_index: 167
  library_dep_index: 66
}
library_dependencies {
  library_index: 166
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 59
}
library_dependencies {
  library_index: 167
  library_dep_index: 8
  library_dep_index: 13
}
library_dependencies {
  library_index: 169
  library_dep_index: 170
  library_dep_index: 171
  library_dep_index: 172
  library_dep_index: 31
  library_dep_index: 65
  library_dep_index: 173
  library_dep_index: 39
}
library_dependencies {
  library_index: 170
  library_dep_index: 8
}
library_dependencies {
  library_index: 173
  library_dep_index: 8
}
library_dependencies {
  library_index: 174
  library_dep_index: 175
  library_dep_index: 145
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 61
  library_dep_index: 173
  library_dep_index: 32
  library_dep_index: 24
  library_dep_index: 27
}
library_dependencies {
  library_index: 175
  library_dep_index: 0
  library_dep_index: 176
}
library_dependencies {
  library_index: 176
  library_dep_index: 0
}
library_dependencies {
  library_index: 177
  library_dep_index: 178
  library_dep_index: 181
  library_dep_index: 179
  library_dep_index: 182
  library_dep_index: 185
  library_dep_index: 188
  library_dep_index: 186
  library_dep_index: 189
  library_dep_index: 190
  library_dep_index: 191
  library_dep_index: 192
  library_dep_index: 8
  library_dep_index: 183
  library_dep_index: 187
  library_dep_index: 184
  library_dep_index: 193
  library_dep_index: 30
  library_dep_index: 194
  library_dep_index: 195
  library_dep_index: 29
  library_dep_index: 129
  library_dep_index: 0
}
library_dependencies {
  library_index: 178
  library_dep_index: 28
  library_dep_index: 179
  library_dep_index: 180
  library_dep_index: 8
  library_dep_index: 15
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 179
  library_dep_index: 180
  library_dep_index: 8
  library_dep_index: 129
}
library_dependencies {
  library_index: 180
  library_dep_index: 100
}
library_dependencies {
  library_index: 181
  library_dep_index: 178
  library_dep_index: 4
  library_dep_index: 179
  library_dep_index: 180
}
library_dependencies {
  library_index: 182
  library_dep_index: 183
  library_dep_index: 184
  library_dep_index: 187
  library_dep_index: 8
}
library_dependencies {
  library_index: 183
  library_dep_index: 8
}
library_dependencies {
  library_index: 184
  library_dep_index: 183
  library_dep_index: 8
  library_dep_index: 100
  library_dep_index: 185
  library_dep_index: 186
}
library_dependencies {
  library_index: 185
  library_dep_index: 8
}
library_dependencies {
  library_index: 186
  library_dep_index: 8
  library_dep_index: 185
}
library_dependencies {
  library_index: 187
  library_dep_index: 183
  library_dep_index: 184
  library_dep_index: 185
  library_dep_index: 188
  library_dep_index: 8
}
library_dependencies {
  library_index: 188
  library_dep_index: 8
  library_dep_index: 185
}
library_dependencies {
  library_index: 189
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 190
  library_dep_index: 191
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 180
  library_dep_index: 178
  library_dep_index: 181
  library_dep_index: 179
}
library_dependencies {
  library_index: 191
  library_dep_index: 29
  library_dep_index: 180
}
library_dependencies {
  library_index: 192
  library_dep_index: 30
  library_dep_index: 180
}
library_dependencies {
  library_index: 193
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 194
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 195
  library_dep_index: 156
  library_dep_index: 30
}
library_dependencies {
  library_index: 197
  library_dep_index: 8
  library_dep_index: 63
  library_dep_index: 0
}
library_dependencies {
  library_index: 198
  library_dep_index: 169
  library_dep_index: 96
  library_dep_index: 8
}
library_dependencies {
  library_index: 199
  library_dep_index: 73
  library_dep_index: 98
  library_dep_index: 8
}
library_dependencies {
  library_index: 200
  library_dep_index: 8
  library_dep_index: 30
}
library_dependencies {
  library_index: 201
  library_dep_index: 149
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 202
  library_dep_index: 203
  library_dep_index: 204
  library_dep_index: 205
  library_dep_index: 206
  library_dep_index: 207
  library_dep_index: 215
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 16
}
library_dependencies {
  library_index: 202
  library_dep_index: 8
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 203
}
library_dependencies {
  library_index: 203
  library_dep_index: 8
  library_dep_index: 15
  library_dep_index: 32
  library_dep_index: 202
  library_dep_index: 126
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 202
}
library_dependencies {
  library_index: 204
  library_dep_index: 8
  library_dep_index: 13
}
library_dependencies {
  library_index: 205
  library_dep_index: 30
}
library_dependencies {
  library_index: 206
  library_dep_index: 30
}
library_dependencies {
  library_index: 207
  library_dep_index: 208
  library_dep_index: 205
  library_dep_index: 30
  library_dep_index: 213
  library_dep_index: 200
}
library_dependencies {
  library_index: 208
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 13
  library_dep_index: 209
  library_dep_index: 212
  library_dep_index: 211
  library_dep_index: 13
  library_dep_index: 49
}
library_dependencies {
  library_index: 209
  library_dep_index: 210
  library_dep_index: 211
  library_dep_index: 212
  library_dep_index: 21
}
library_dependencies {
  library_index: 210
  library_dep_index: 8
}
library_dependencies {
  library_index: 211
  library_dep_index: 8
  library_dep_index: 212
}
library_dependencies {
  library_index: 212
  library_dep_index: 8
}
library_dependencies {
  library_index: 213
  library_dep_index: 30
  library_dep_index: 214
}
library_dependencies {
  library_index: 214
  library_dep_index: 30
}
library_dependencies {
  library_index: 215
  library_dep_index: 193
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 216
  library_dep_index: 13
  library_dep_index: 217
}
library_dependencies {
  library_index: 217
  library_dep_index: 27
  library_dep_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 218
  library_dep_index: 84
  library_dep_index: 74
  library_dep_index: 77
  library_dep_index: 219
}
library_dependencies {
  library_index: 219
  library_dep_index: 84
  library_dep_index: 220
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 85
  library_dep_index: 83
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 221
  library_dep_index: 4
}
library_dependencies {
  library_index: 220
  library_dep_index: 82
  library_dep_index: 77
  library_dep_index: 83
  library_dep_index: 4
}
library_dependencies {
  library_index: 221
  library_dep_index: 85
  library_dep_index: 82
  library_dep_index: 77
  library_dep_index: 222
  library_dep_index: 220
  library_dep_index: 4
}
library_dependencies {
  library_index: 222
  library_dep_index: 4
}
library_dependencies {
  library_index: 223
  library_dep_index: 74
  library_dep_index: 84
  library_dep_index: 77
  library_dep_index: 85
  library_dep_index: 81
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 218
  dependency_index: 223
  dependency_index: 199
  dependency_index: 24
  dependency_index: 27
  dependency_index: 32
  dependency_index: 3
  dependency_index: 101
  dependency_index: 8
  dependency_index: 102
  dependency_index: 40
  dependency_index: 123
  dependency_index: 124
  dependency_index: 143
  dependency_index: 96
  dependency_index: 99
  dependency_index: 144
  dependency_index: 145
  dependency_index: 6
  dependency_index: 146
  dependency_index: 147
  dependency_index: 78
  dependency_index: 162
  dependency_index: 149
  dependency_index: 150
  dependency_index: 151
  dependency_index: 160
  dependency_index: 163
  dependency_index: 164
  dependency_index: 168
  dependency_index: 97
  dependency_index: 169
  dependency_index: 61
  dependency_index: 173
  dependency_index: 174
  dependency_index: 13
  dependency_index: 18
  dependency_index: 37
  dependency_index: 34
  dependency_index: 177
  dependency_index: 196
  dependency_index: 175
  dependency_index: 197
  dependency_index: 198
  dependency_index: 98
  dependency_index: 200
  dependency_index: 201
  dependency_index: 216
  dependency_index: 217
  dependency_index: 60
  dependency_index: 72
  dependency_index: 52
  dependency_index: 204
}
repositories {
  maven_repo {
    url: "https://oss.sonatype.org/content/repositories/snapshots/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://www.jitpack.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://www.jitpack.io"
  }
}
