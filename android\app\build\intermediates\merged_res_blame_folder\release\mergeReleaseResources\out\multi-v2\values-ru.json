{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-76:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "71,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5473,5580,5746,5872,5982,6124,6253,6368,6629,6810,6917,7080,7206,7373,7531,7600,7660", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "5575,5741,5867,5977,6119,6248,6363,6467,6805,6912,7075,7201,7368,7526,7595,7655,7741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,291,354,421,488,588,652,814,952,1084,1134,1194,1338,1426,1475,1556,1593,1630,1677,1758,1805", "endColumns": "41,49,62,66,66,99,63,161,137,131,49,59,143,87,48,80,36,36,46,80,46,55", "endOffsets": "240,290,353,420,487,587,651,813,951,1083,1133,1193,1337,1425,1474,1555,1592,1629,1676,1757,1804,1860"}, "to": {"startLines": "205,206,207,210,211,212,213,214,215,216,217,218,219,220,227,228,229,230,231,232,233,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16962,17008,17062,17299,17370,17441,17545,17613,17779,17921,18057,18111,18175,18323,18924,18977,19062,19103,19144,19195,19280,21305", "endColumns": "45,53,66,70,70,103,67,165,141,135,53,63,147,91,52,84,40,40,50,84,50,59", "endOffsets": "17003,17057,17124,17365,17436,17540,17608,17774,17916,18052,18106,18170,18318,18410,18972,19057,19098,19139,19190,19275,19326,21360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,336,420,517,654,746,812,911,988,1051,1169,1234,1291,1361,1422,1476,1592,1649,1711,1765,1839,1967,2055,2141,2248,2332,2417,2508,2575,2641,2713,2791,2887,2967,3043,3120,3197,3286,3359,3449,3544,3618,3699,3792,3847,3913,3999,4084,4146,4210,4273,4371,4471,4566,4668", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "331,415,512,649,741,807,906,983,1046,1164,1229,1286,1356,1417,1471,1587,1644,1706,1760,1834,1962,2050,2136,2243,2327,2412,2503,2570,2636,2708,2786,2882,2962,3038,3115,3192,3281,3354,3444,3539,3613,3694,3787,3842,3908,3994,4079,4141,4205,4268,4366,4466,4561,4663,4743"}, "to": {"startLines": "23,57,65,66,67,93,145,150,155,157,158,159,160,161,162,163,164,165,166,167,168,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,221", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "999,4071,4882,4979,5116,8146,12082,12577,12990,13122,13240,13305,13362,13432,13493,13547,13663,13720,13782,13836,13910,14261,14349,14435,14542,14626,14711,14802,14869,14935,15007,15085,15181,15261,15337,15414,15491,15580,15653,15743,15838,15912,15993,16086,16141,16207,16293,16378,16440,16504,16567,16665,16765,16860,18415", "endLines": "28,57,65,66,67,93,145,150,155,157,158,159,160,161,162,163,164,165,166,167,168,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,221", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "1275,4150,4974,5111,5203,8207,12176,12649,13048,13235,13300,13357,13427,13488,13542,13658,13715,13777,13831,13905,14033,14344,14430,14537,14621,14706,14797,14864,14930,15002,15080,15176,15256,15332,15409,15486,15575,15648,15738,15833,15907,15988,16081,16136,16202,16288,16373,16435,16499,16562,16660,16760,16855,16957,18490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,949,1032,1115,1198,1288,1388,1459,1532,1631,1732,1805,1877,1942,2020,2132,2243,2360,2437,2532,2604,2677,2765,2853,2922,2987,3040,3102,3150,3211,3278,3346,3412,3494,3552,3609,3675,3740,3806,3858,3919,4004,4089", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "275,614,944,1027,1110,1193,1283,1383,1454,1527,1626,1727,1800,1872,1937,2015,2127,2238,2355,2432,2527,2599,2672,2760,2848,2917,2982,3035,3097,3145,3206,3273,3341,3407,3489,3547,3604,3670,3735,3801,3853,3914,3999,4084,4147"}, "to": {"startLines": "2,11,17,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,330,669,8212,8295,8378,8461,8551,8651,8722,8795,8894,8995,9068,9140,9205,9283,9395,9506,9623,9700,9795,9867,9940,10028,10116,10185,10917,10970,11032,11080,11141,11208,11276,11342,11424,11482,11539,11605,11670,11736,11788,11849,11934,12019", "endLines": "10,16,22,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "325,664,994,8290,8373,8456,8546,8646,8717,8790,8889,8990,9063,9135,9200,9278,9390,9501,9618,9695,9790,9862,9935,10023,10111,10180,10245,10965,11027,11075,11136,11203,11271,11337,11419,11477,11534,11600,11665,11731,11783,11844,11929,12014,12077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "89,146,147,148", "startColumns": "4,4,4,4", "startOffsets": "7746,12181,12286,12398", "endColumns": "107,104,111,104", "endOffsets": "7849,12281,12393,12498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10250,10322,10383,10448,10514,10592,10666,10754,10840", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "10317,10378,10443,10509,10587,10661,10749,10835,10912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "79", "startColumns": "4", "startOffsets": "6472", "endColumns": "156", "endOffsets": "6624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,235", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1280,1395,1497,1596,1682,1787,1908,1987,2063,2155,2249,2344,2437,2532,2626,2722,2817,2909,3001,3090,3196,3303,3401,3510,3617,3731,3897,19415", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "1390,1492,1591,1677,1782,1903,1982,2058,2150,2244,2339,2432,2527,2621,2717,2812,2904,2996,3085,3191,3298,3396,3505,3612,3726,3892,3992,19492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "58,59,60,61,62,63,64,248", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4155,4253,4355,4456,4557,4662,4765,20441", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "4248,4350,4451,4552,4657,4760,4877,20537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,218,292,367,456,525,592,669,748,837,934,1006,1090,1183,1258,1340,1423,1500,1572,1647,1732,1804,1884,1954", "endColumns": "73,88,73,74,88,68,66,76,78,88,96,71,83,92,74,81,82,76,71,74,84,71,79,69,84", "endOffsets": "124,213,287,362,451,520,587,664,743,832,929,1001,1085,1178,1253,1335,1418,1495,1567,1642,1727,1799,1879,1949,2034"}, "to": {"startLines": "56,70,149,151,152,156,169,170,171,222,223,226,234,237,238,239,241,242,244,246,247,249,252,254,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3997,5384,12503,12654,12729,13053,14038,14105,14182,18495,18584,18852,19331,19569,19662,19737,19895,19978,20133,20281,20356,20542,20768,20971,21041", "endColumns": "73,88,73,74,88,68,66,76,78,88,96,71,83,92,74,81,82,76,71,74,84,71,79,69,84", "endOffsets": "4066,5468,12572,12724,12813,13117,14100,14177,14256,18579,18676,18919,19410,19657,19732,19814,19973,20050,20200,20351,20436,20609,20843,21036,21121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,999,1086,1158,1234,1312,1388,1472,1542", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,75,77,75,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,994,1081,1153,1229,1307,1383,1467,1537,1660"}, "to": {"startLines": "68,69,90,91,92,153,154,208,209,224,225,236,240,243,245,250,251,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5208,5301,7854,7952,8054,12818,12900,17129,17217,18681,18765,19497,19819,20055,20205,20614,20698,20848", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,75,77,75,83,69,122", "endOffsets": "5296,5379,7947,8049,8141,12895,12985,17212,17294,18760,18847,19564,19890,20128,20276,20693,20763,20966"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "256,257", "startColumns": "4,4", "startOffsets": "21126,21214", "endColumns": "87,90", "endOffsets": "21209,21300"}}]}]}