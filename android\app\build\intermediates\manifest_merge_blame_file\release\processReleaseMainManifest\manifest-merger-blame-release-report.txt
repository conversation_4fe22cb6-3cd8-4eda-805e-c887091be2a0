1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gokul719.snack97152fc1f368437dac54171df4ba22bd"
4    android:versionCode="1"
5    android:versionName="1.2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:3-62
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:20-60
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:3-64
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:20-62
13    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:3-77
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:20-75
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:3-77
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:20-75
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:3-68
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:20-66
16    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:3-75
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:20-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:3-63
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:20-61
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:3-78
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:20-76
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:3-76
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:20-74
20
21    <queries>
21-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:3-17:13
22        <intent>
22-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:12:5-16:14
23            <action android:name="android.intent.action.VIEW" />
23-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:7-58
23-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:15-56
24
25            <category android:name="android.intent.category.BROWSABLE" />
25-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-67
25-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:17-65
26
27            <data android:scheme="https" />
27-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
27-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:13-35
28        </intent>
29        <!-- Query open documents -->
30        <intent>
30-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
31            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
31-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
31-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
32        </intent>
33        <intent>
33-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-19:18
34
35            <!-- Required for picking images from the camera roll if targeting API 30 -->
36            <action android:name="android.media.action.IMAGE_CAPTURE" />
36-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-73
36-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:21-70
37        </intent>
38        <intent>
38-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-24:18
39
40            <!-- Required for picking images from the camera if targeting API 30 -->
41            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
41-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-80
41-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:21-77
42        </intent>
43        <intent>
43-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-14:18
44
45            <!-- Required for file sharing if targeting API 30 -->
46            <action android:name="android.intent.action.SEND" />
46-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-65
46-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-62
47
48            <data android:mimeType="*/*" />
48-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
49        </intent>
50        <intent>
50-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
51
52            <!-- Required for text-to-speech if targeting API 30 -->
53            <action android:name="android.intent.action.TTS_SERVICE" />
53-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-72
53-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-69
54        </intent>
55        <intent>
55-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
56            <action android:name="android.intent.action.GET_CONTENT" />
56-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
56-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
57
58            <category android:name="android.intent.category.OPENABLE" />
58-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
58-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
59
60            <data android:mimeType="*/*" />
60-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
61        </intent> <!-- End of browser content -->
62        <!-- For CustomTabsService -->
63        <intent>
63-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:47:9-49:18
64            <action android:name="android.support.customtabs.action.CustomTabsService" />
64-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:48:13-90
64-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:48:21-87
65        </intent> <!-- End of CustomTabsService -->
66        <!-- For MRAID capabilities -->
67        <intent>
67-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:52:9-56:18
68            <action android:name="android.intent.action.INSERT" />
68-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:53:13-67
68-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:53:21-64
69
70            <data android:mimeType="vnd.android.cursor.dir/event" />
70-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
71        </intent>
72        <intent>
72-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:57:9-61:18
73            <action android:name="android.intent.action.VIEW" />
73-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:7-58
73-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:15-56
74
75            <data android:scheme="sms" />
75-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
75-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:13-35
76        </intent>
77        <intent>
77-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:62:9-66:18
78            <action android:name="android.intent.action.DIAL" />
78-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:63:13-65
78-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:63:21-62
79
80            <data android:path="tel:" />
80-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
81        </intent>
82    </queries>
83
84    <uses-permission android:name="android.permission.WAKE_LOCK" />
84-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-68
84-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-65
85    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
85-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-79
85-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-76
86    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
86-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-81
86-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-78
87    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
87-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
87-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
88    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
88-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:27:5-82
88-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:27:22-79
89    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
89-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:28:5-88
89-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:28:22-85
90    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
90-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:29:5-83
90-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:29:22-80
91    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
91-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
91-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
92    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
92-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
92-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
93
94    <permission
94-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
95        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
95-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
96        android:protectionLevel="signature" />
96-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
97
98    <uses-permission android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
99    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
99-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
99-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65e83cd3aee2760cef02a821b313c704\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
100    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
101    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
102    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
103    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
104    <!-- for Samsung -->
105    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
106    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
106-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
106-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
107    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
107-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
107-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
108    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
108-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
108-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
109    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
109-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
109-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
110    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
110-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
110-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
111    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
111-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
111-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
112    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
113    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
114    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
115    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
116    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
117    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
118    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
119    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
120    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62dd2806f6eaf3b3dfff1dc942dddd5c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
121
122    <application
122-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:3-37:17
123        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainApplication"
123-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:16-47
124        android:allowBackup="true"
124-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:162-188
125        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
125-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:248-316
126        android:extractNativeLibs="false"
127        android:icon="@mipmap/ic_launcher"
127-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:81-115
128        android:label="@string/app_name"
128-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:48-80
129        android:roundIcon="@mipmap/ic_launcher_round"
129-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:116-161
130        android:supportsRtl="true"
130-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:221-247
131        android:theme="@style/AppTheme" >
131-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:189-220
132        <meta-data
132-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:5-159
133            android:name="com.google.android.gms.ads.APPLICATION_ID"
133-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:16-72
134            android:value="ca-app-pub-9706687137550019~9208363455" />
134-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:73-127
135        <meta-data
135-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:5-137
136            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
136-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:16-84
137            android:value="true" />
137-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:85-105
138        <meta-data
138-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:5-83
139            android:name="expo.modules.updates.ENABLED"
139-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:16-59
140            android:value="false" />
140-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:60-81
141        <meta-data
141-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:5-105
142            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
142-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:16-80
143            android:value="ALWAYS" />
143-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:81-103
144        <meta-data
144-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:5-99
145            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
145-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:16-79
146            android:value="0" />
146-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:80-97
147
148        <activity
148-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:5-36:16
149            android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainActivity"
149-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:15-43
150            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
150-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:44-134
151            android:exported="true"
151-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:256-279
152            android:launchMode="singleTask"
152-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:135-166
153            android:screenOrientation="portrait"
153-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:280-316
154            android:theme="@style/Theme.App.SplashScreen"
154-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:210-255
155            android:windowSoftInputMode="adjustResize" >
155-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:167-209
156            <intent-filter>
156-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:7-28:23
157                <action android:name="android.intent.action.MAIN" />
157-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:9-60
157-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:17-58
158
159                <category android:name="android.intent.category.LAUNCHER" />
159-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:9-68
159-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:19-66
160            </intent-filter>
161            <intent-filter>
161-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:7-35:23
162                <action android:name="android.intent.action.VIEW" />
162-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:7-58
162-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:15-56
163
164                <category android:name="android.intent.category.DEFAULT" />
164-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:31:9-67
164-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:31:19-65
165                <category android:name="android.intent.category.BROWSABLE" />
165-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-67
165-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:17-65
166
167                <data android:scheme="com.gokul719.snack97152fc1f368437dac54171df4ba22bd" />
167-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
167-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:13-35
168                <data android:scheme="exp+snack-97152fc1-f368-437d-ac54-171df4ba22bd" />
168-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
168-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:13-35
169            </intent-filter>
170        </activity>
171
172        <meta-data
172-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-21:36
173            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
173-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
174            android:value="true" />
174-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-33
175        <meta-data
175-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:9-24:36
176            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
176-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-79
177            android:value="true" />
177-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-33
178        <!--
179           This may generate a warning during your build:
180
181           > property#android.adservices.AD_SERVICES_CONFIG@android:resource
182           > was tagged at AndroidManifest.xml:23 to replace other declarations
183           > but no other declaration present
184
185           You may safely ignore this warning.
186
187           We must include this in case you also use Firebase Analytics in some
188           of its configurations, as it may also include this file, and the two
189           will collide and cause a build error if we don't set this one to take
190           priority via replacement.
191
192           https://github.com/invertase/react-native-google-mobile-ads/issues/657
193        -->
194        <property
194-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-45:48
195            android:name="android.adservices.AD_SERVICES_CONFIG"
195-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-65
196            android:resource="@xml/gma_ad_services_config" />
196-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-59
197
198        <provider
198-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
199            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
199-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-83
200            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.fileprovider"
200-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
201            android:exported="false"
201-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
202            android:grantUriPermissions="true" >
202-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
203            <meta-data
203-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
204                android:name="android.support.FILE_PROVIDER_PATHS"
204-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
205                android:resource="@xml/file_provider_paths" />
205-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
206        </provider>
207        <provider
207-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-15:20
208            android:name="expo.modules.clipboard.ClipboardFileProvider"
208-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
209            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ClipboardFileProvider"
209-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-73
210            android:exported="true" >
210-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-36
211            <meta-data
211-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-14:68
212                android:name="expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS"
212-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:17-84
213                android:resource="@xml/clipboard_provider_paths" />
213-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-65
214        </provider>
215        <provider
215-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
216            android:name="expo.modules.filesystem.FileSystemFileProvider"
216-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
217            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.FileSystemFileProvider"
217-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
218            android:exported="false"
218-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
219            android:grantUriPermissions="true" >
219-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
220            <meta-data
220-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
221                android:name="android.support.FILE_PROVIDER_PATHS"
221-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
222                android:resource="@xml/file_system_provider_paths" />
222-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
223        </provider>
224
225        <service
225-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-40:19
226            android:name="com.google.android.gms.metadata.ModuleDependencies"
226-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-78
227            android:enabled="false"
227-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-36
228            android:exported="false" >
228-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-37
229            <intent-filter>
229-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-35:29
230                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
230-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:17-94
230-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:25-91
231            </intent-filter>
232
233            <meta-data
233-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-39:36
234                android:name="photopicker_activity:0:required"
234-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-63
235                android:value="" />
235-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:17-33
236        </service>
237
238        <activity
238-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-44:59
239            android:name="com.canhub.cropper.CropImageActivity"
239-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-64
240            android:exported="true"
240-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
241            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
241-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-56
242        <provider
242-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:9-54:20
243            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
243-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-89
244            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ImagePickerFileProvider"
244-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:13-75
245            android:exported="false"
245-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:13-37
246            android:grantUriPermissions="true" >
246-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-47
247            <meta-data
247-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
248                android:name="android.support.FILE_PROVIDER_PATHS"
248-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
249                android:resource="@xml/image_picker_provider_paths" />
249-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
250        </provider>
251
252        <service
252-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-17:19
253            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
253-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-91
254            android:exported="false" >
254-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
255            <intent-filter android:priority="-1" >
255-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
255-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
256                <action android:name="com.google.firebase.MESSAGING_EVENT" />
256-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
256-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
257            </intent-filter>
258        </service>
259
260        <receiver
260-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:20
261            android:name="expo.modules.notifications.service.NotificationsService"
261-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
262            android:enabled="true"
262-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-35
263            android:exported="false" >
263-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
264            <intent-filter android:priority="-1" >
264-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-30:29
264-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:28-49
265                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
265-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:17-88
265-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:25-85
266                <action android:name="android.intent.action.BOOT_COMPLETED" />
266-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
266-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
267                <action android:name="android.intent.action.REBOOT" />
267-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:17-71
267-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:25-68
268                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
268-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:17-82
268-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:25-79
269                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
269-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-82
269-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-79
270                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
270-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-84
270-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:25-81
271            </intent-filter>
272        </receiver>
273
274        <activity
274-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-40:75
275            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
275-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-92
276            android:excludeFromRecents="true"
276-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-46
277            android:exported="false"
277-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-37
278            android:launchMode="standard"
278-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-42
279            android:noHistory="true"
279-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-37
280            android:taskAffinity=""
280-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-36
281            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
281-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-72
282
283        <provider
283-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-26:20
284            android:name="expo.modules.sharing.SharingFileProvider"
284-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-68
285            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.SharingFileProvider"
285-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-71
286            android:exported="false"
286-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-37
287            android:grantUriPermissions="true" >
287-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-47
288            <meta-data
288-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
289                android:name="android.support.FILE_PROVIDER_PATHS"
289-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
290                android:resource="@xml/sharing_provider_paths" />
290-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
291        </provider>
292
293        <meta-data
293-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
294            android:name="org.unimodules.core.AppLoader#react-native-headless"
294-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
295            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
295-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
296        <meta-data
296-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
297            android:name="com.facebook.soloader.enabled"
297-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
298            android:value="true" />
298-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
299        <meta-data
299-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:11:9-13:43
300            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
300-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:12:13-84
301            android:value="GlideModule" />
301-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:13:13-40
302
303        <provider
303-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
304            android:name="com.canhub.cropper.CropFileProvider"
304-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
305            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.cropper.fileprovider"
305-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
306            android:exported="false"
306-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
307            android:grantUriPermissions="true" >
307-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
308            <meta-data
308-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
309                android:name="android.support.FILE_PROVIDER_PATHS"
309-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
310                android:resource="@xml/library_file_paths" />
310-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
311        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
312        <activity
312-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:73:9-78:43
313            android:name="com.google.android.gms.ads.AdActivity"
313-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:74:13-65
314            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
314-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:75:13-122
315            android:exported="false"
315-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:76:13-37
316            android:theme="@android:style/Theme.Translucent" />
316-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:77:13-61
317
318        <provider
318-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:80:9-85:43
319            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
319-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:81:13-76
320            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.mobileadsinitprovider"
320-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:82:13-73
321            android:exported="false"
321-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:83:13-37
322            android:initOrder="100" />
322-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:84:13-36
323
324        <service
324-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:87:9-91:43
325            android:name="com.google.android.gms.ads.AdService"
325-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:88:13-64
326            android:enabled="true"
326-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:89:13-35
327            android:exported="false" />
327-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:90:13-37
328
329        <activity
329-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:93:9-97:43
330            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
330-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:94:13-82
331            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
331-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:95:13-122
332            android:exported="false" />
332-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:96:13-37
333        <activity
333-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:98:9-105:43
334            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
334-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:99:13-82
335            android:excludeFromRecents="true"
335-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:100:13-46
336            android:exported="false"
336-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:101:13-37
337            android:launchMode="singleTask"
337-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:102:13-44
338            android:taskAffinity=""
338-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:103:13-36
339            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
339-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:104:13-72
340
341        <receiver
341-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
342            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
342-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
343            android:exported="true"
343-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
344            android:permission="com.google.android.c2dm.permission.SEND" >
344-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
345            <intent-filter>
345-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
346                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
346-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
346-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
347            </intent-filter>
348
349            <meta-data
349-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
350                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
350-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
351                android:value="true" />
351-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
352        </receiver>
353        <!--
354             FirebaseMessagingService performs security checks at runtime,
355             but set to not exported to explicitly avoid allowing another app to call it.
356        -->
357        <service
357-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
358            android:name="com.google.firebase.messaging.FirebaseMessagingService"
358-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
359            android:directBootAware="true"
359-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
360            android:exported="false" >
360-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
361            <intent-filter android:priority="-500" >
361-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
361-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
362                <action android:name="com.google.firebase.MESSAGING_EVENT" />
362-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
362-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
363            </intent-filter>
364        </service>
365        <service
365-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
366            android:name="com.google.firebase.components.ComponentDiscoveryService"
366-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
367            android:directBootAware="true"
367-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
368            android:exported="false" >
368-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
369            <meta-data
369-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
370                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
370-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
371                android:value="com.google.firebase.components.ComponentRegistrar" />
371-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
372            <meta-data
372-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
373                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
373-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
374                android:value="com.google.firebase.components.ComponentRegistrar" />
374-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3e1f9edba8e6e0a6f12715746c91a00\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
375            <meta-data
375-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
376                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
376-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
377                android:value="com.google.firebase.components.ComponentRegistrar" />
377-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
378            <meta-data
378-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
379                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
379-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
380                android:value="com.google.firebase.components.ComponentRegistrar" />
380-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30ae95496aa3db3acf41d4473d7e0123\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
381            <meta-data
381-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
382                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
382-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
383                android:value="com.google.firebase.components.ComponentRegistrar" />
383-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91477e5d6c9216b70ac507e0b5e1f66e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
384            <meta-data
384-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
385                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
385-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
386                android:value="com.google.firebase.components.ComponentRegistrar" />
386-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
387            <meta-data
387-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
388                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
388-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
389                android:value="com.google.firebase.components.ComponentRegistrar" />
389-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10cf65477e02ebee6ddd7715742cb3aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
390        </service>
391
392        <provider
392-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
393            android:name="com.google.firebase.provider.FirebaseInitProvider"
393-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
394            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.firebaseinitprovider"
394-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
395            android:directBootAware="true"
395-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
396            android:exported="false"
396-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
397            android:initOrder="100" />
397-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6875a84b61b4ac85977fcfbac31feb84\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
398
399        <activity
399-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
400            android:name="com.google.android.gms.common.api.GoogleApiActivity"
400-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
401            android:exported="false"
401-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
402            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
402-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc43d647cd71a26bfd564415b69e8be4\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
403
404        <provider
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
405            android:name="androidx.startup.InitializationProvider"
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
406            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.androidx-startup"
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
407            android:exported="false" >
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
408            <meta-data
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
409                android:name="androidx.work.WorkManagerInitializer"
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
410                android:value="androidx.startup" />
410-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
411            <meta-data
411-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
412                android:name="androidx.emoji2.text.EmojiCompatInitializer"
412-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
413                android:value="androidx.startup" />
413-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
414            <meta-data
414-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
415                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
415-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
416                android:value="androidx.startup" />
416-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
417            <meta-data
417-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
418                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
419                android:value="androidx.startup" />
419-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
420        </provider>
421
422        <service
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
423            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
424            android:directBootAware="false"
424-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
425            android:enabled="@bool/enable_system_alarm_service_default"
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
426            android:exported="false" />
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
427        <service
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
428            android:name="androidx.work.impl.background.systemjob.SystemJobService"
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
429            android:directBootAware="false"
429-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
430            android:enabled="@bool/enable_system_job_service_default"
430-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
431            android:exported="true"
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
432            android:permission="android.permission.BIND_JOB_SERVICE" />
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
433        <service
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
434            android:name="androidx.work.impl.foreground.SystemForegroundService"
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
435            android:directBootAware="false"
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
436            android:enabled="@bool/enable_system_foreground_service_default"
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
437            android:exported="false" />
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
438
439        <receiver
439-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
440            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
440-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
441            android:directBootAware="false"
441-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
442            android:enabled="true"
442-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
443            android:exported="false" />
443-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
444        <receiver
444-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
445            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
445-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
446            android:directBootAware="false"
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
447            android:enabled="false"
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
448            android:exported="false" >
448-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
449            <intent-filter>
449-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
450                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
450-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
450-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
451                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
451-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
451-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
452            </intent-filter>
453        </receiver>
454        <receiver
454-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
455            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
455-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
456            android:directBootAware="false"
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
457            android:enabled="false"
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
458            android:exported="false" >
458-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
459            <intent-filter>
459-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
460                <action android:name="android.intent.action.BATTERY_OKAY" />
460-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
460-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
461                <action android:name="android.intent.action.BATTERY_LOW" />
461-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
461-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
462            </intent-filter>
463        </receiver>
464        <receiver
464-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
465            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
465-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
466            android:directBootAware="false"
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
467            android:enabled="false"
467-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
468            android:exported="false" >
468-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
469            <intent-filter>
469-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
470                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
470-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
470-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
471                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
471-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
471-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
472            </intent-filter>
473        </receiver>
474        <receiver
474-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
475            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
475-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
476            android:directBootAware="false"
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
477            android:enabled="false"
477-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
478            android:exported="false" >
478-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
479            <intent-filter>
479-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
480                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
480-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
480-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
481            </intent-filter>
482        </receiver>
483        <receiver
483-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
484            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
484-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
485            android:directBootAware="false"
485-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
486            android:enabled="false"
486-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
487            android:exported="false" >
487-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
488            <intent-filter>
488-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
489                <action android:name="android.intent.action.BOOT_COMPLETED" />
489-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
489-->[:expo-notifications] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
490                <action android:name="android.intent.action.TIME_SET" />
490-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
490-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
491                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
491-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
491-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
492            </intent-filter>
493        </receiver>
494        <receiver
494-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
495            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
495-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
496            android:directBootAware="false"
496-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
497            android:enabled="@bool/enable_system_alarm_service_default"
497-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
498            android:exported="false" >
498-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
499            <intent-filter>
499-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
500                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
500-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
500-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
501            </intent-filter>
502        </receiver>
503        <receiver
503-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
504            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
504-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
505            android:directBootAware="false"
505-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
506            android:enabled="true"
506-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
507            android:exported="true"
507-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
508            android:permission="android.permission.DUMP" >
508-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
509            <intent-filter>
509-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
510                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
510-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
510-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
511            </intent-filter>
512        </receiver>
513
514        <uses-library
514-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
515            android:name="android.ext.adservices"
515-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
516            android:required="false" />
516-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
517
518        <meta-data
518-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
519            android:name="com.google.android.gms.version"
519-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
520            android:value="@integer/google_play_services_version" />
520-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
521
522        <receiver
522-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
523            android:name="androidx.profileinstaller.ProfileInstallReceiver"
523-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
524            android:directBootAware="false"
524-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
525            android:enabled="true"
525-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
526            android:exported="true"
526-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
527            android:permission="android.permission.DUMP" >
527-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
528            <intent-filter>
528-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
529                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
529-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
529-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
530            </intent-filter>
531            <intent-filter>
531-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
532                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
532-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
532-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
533            </intent-filter>
534            <intent-filter>
534-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
535                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
535-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
535-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
536            </intent-filter>
537            <intent-filter>
537-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
538                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
538-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
538-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
539            </intent-filter>
540        </receiver>
541
542        <service
542-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
543            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
543-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
544            android:exported="false" >
544-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
545            <meta-data
545-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
546                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
546-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
547                android:value="cct" />
547-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ade9e77bc19b03888b832329bcd4dfac\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
548        </service>
549        <service
549-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
550            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
550-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
551            android:exported="false"
551-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
552            android:permission="android.permission.BIND_JOB_SERVICE" >
552-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
553        </service>
554
555        <receiver
555-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
556            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
556-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
557            android:exported="false" />
557-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad1c8134fb0878b4e82007d47930701\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
558
559        <service
559-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
560            android:name="androidx.room.MultiInstanceInvalidationService"
560-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
561            android:directBootAware="true"
561-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
562            android:exported="false" />
562-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
563    </application>
564
565</manifest>
