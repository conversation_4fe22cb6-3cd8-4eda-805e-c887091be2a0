# C/C++ build system timings
generate_cxx_metadata
  [gap of 72ms]
  create-invalidation-state 10ms
  generate-prefab-packages
    [gap of 142ms]
    exec-prefab 1233ms
    [gap of 92ms]
  generate-prefab-packages completed in 1467ms
  execute-generate-process
    exec-configure 4381ms
    [gap of 445ms]
  execute-generate-process completed in 4831ms
  [gap of 87ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 6539ms

