{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-76:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,172,232,288,360,419,501,581", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "109,167,227,283,355,414,496,576,639"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8927,8986,9044,9104,9160,9232,9291,9373,9453", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "8981,9039,9099,9155,9227,9286,9368,9448,9511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "811,908,1002,1103,1185,1283,1389,1469,1544,1635,1728,1823,1917,2017,2110,2205,2299,2390,2481,2561,2659,2753,2848,2948,3045,3145,3297,17166", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "903,997,1098,1180,1278,1384,1464,1539,1630,1723,1818,1912,2012,2105,2200,2294,2385,2476,2556,2654,2748,2843,2943,3040,3140,3292,3386,17240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-ko\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,238,284,333,393,456,534,595,692,784,884,934,985,1068,1134,1168,1229,1259,1289,1327,1382,1416", "endColumns": "38,45,48,59,62,77,60,96,91,99,49,50,82,65,33,60,29,29,37,54,33,55", "endOffsets": "237,283,332,392,455,533,594,691,783,883,933,984,1067,1133,1167,1228,1258,1288,1326,1381,1415,1471"}, "to": {"startLines": "207,208,209,212,213,214,215,216,217,218,219,220,221,222,230,231,232,233,234,235,236,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15135,15178,15228,15439,15503,15570,15652,15717,15818,15914,16018,16072,16127,16214,16783,16821,16886,16920,16954,16996,17055,18918", "endColumns": "42,49,52,63,66,81,64,100,95,103,53,54,86,69,37,64,33,33,41,58,37,59", "endOffsets": "15173,15223,15276,15498,15565,15647,15712,15813,15909,16013,16067,16122,16209,16279,16816,16881,16915,16949,16991,17050,17088,18973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,260,348,438,518,593,672,751,830,903,979,1047,1123,1197,1267,1341,1405", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,75,73,69,73,63,113", "endOffsets": "179,255,343,433,513,588,667,746,825,898,974,1042,1118,1192,1262,1336,1400,1514"}, "to": {"startLines": "62,63,87,88,89,155,156,210,211,227,228,239,243,246,248,253,254,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4467,4546,6908,6996,7086,11469,11544,15281,15360,16568,16641,17245,17537,17761,17905,18295,18369,18504", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,75,73,69,73,63,113", "endOffsets": "4541,4617,6991,7081,7161,11539,11618,15355,15434,16636,16712,17308,17608,17830,17970,18364,18428,18613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc43d647cd71a26bfd564415b69e8be4\\transformed\\play-services-base-18.0.1\\res\\values-ko\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,428,540,638,749,862,965,1056,1197,1296,1428,1542,1656,1771,1826,1880", "endColumns": "99,134,111,97,110,112,102,90,140,98,131,113,113,114,54,53,70", "endOffsets": "292,427,539,637,748,861,964,1055,1196,1295,1427,1541,1655,1770,1825,1879,1950"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4694,4798,4937,5053,5155,5270,5387,5494,5710,5855,5958,6094,6212,6330,6449,6508,6566", "endColumns": "103,138,115,101,114,116,106,94,144,102,135,117,117,118,58,57,74", "endOffsets": "4793,4932,5048,5150,5265,5382,5489,5584,5850,5953,6089,6207,6325,6444,6503,6561,6636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,78", "endOffsets": "132,211"}, "to": {"startLines": "259,260", "startColumns": "4,4", "startOffsets": "18757,18839", "endColumns": "81,78", "endOffsets": "18834,18913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\11b33a2f8f4c91a9de31219b5632fe82\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,183,236,278,335,390,443,493", "endColumns": "80,46,52,41,56,54,52,49,59", "endOffsets": "131,178,231,273,330,385,438,488,548"}, "to": {"startLines": "84,85,86,147,148,149,150,151,224", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6727,6808,6855,11006,11048,11105,11160,11213,16356", "endColumns": "80,46,52,41,56,54,52,49,59", "endOffsets": "6803,6850,6903,11043,11100,11155,11208,11258,16411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,273,356,462,537,599,680,742,799,886,944,1002,1061,1118,1172,1267,1323,1380,1434,1500,1604,1679,1756,1847,1912,1977,2056,2123,2189,2253,2323,2400,2468,2539,2606,2676,2756,2833,2913,2995,3067,3132,3204,3252,3316,3391,3468,3530,3594,3657,3741,3820,3900,3980", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,68,82,105,74,61,80,61,56,86,57,57,58,56,53,94,55,56,53,65,103,74,76,90,64,64,78,66,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,71", "endOffsets": "199,268,351,457,532,594,675,737,794,881,939,997,1056,1113,1167,1262,1318,1375,1429,1495,1599,1674,1751,1842,1907,1972,2051,2118,2184,2248,2318,2395,2463,2534,2601,2671,2751,2828,2908,2990,3062,3127,3199,3247,3311,3386,3463,3525,3589,3652,3736,3815,3895,3975,4047"}, "to": {"startLines": "19,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "662,3457,4203,4286,4392,7166,10568,11263,11623,11745,11832,11890,11948,12007,12064,12118,12213,12269,12326,12380,12446,12759,12834,12911,13002,13067,13132,13211,13278,13344,13408,13478,13555,13623,13694,13761,13831,13911,13988,14068,14150,14222,14287,14359,14407,14471,14546,14623,14685,14749,14812,14896,14975,15055,16284", "endLines": "22,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,223", "endColumns": "12,68,82,105,74,61,80,61,56,86,57,57,58,56,53,94,55,56,53,65,103,74,76,90,64,64,78,66,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,71", "endOffsets": "806,3521,4281,4387,4462,7223,10644,11320,11675,11827,11885,11943,12002,12059,12113,12208,12264,12321,12375,12441,12545,12829,12906,12997,13062,13127,13206,13273,13339,13403,13473,13550,13618,13689,13756,13826,13906,13983,14063,14145,14217,14282,14354,14402,14466,14541,14618,14680,14744,14807,14891,14970,15050,15130,16351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-ko\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "116", "endOffsets": "311"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5589", "endColumns": "120", "endOffsets": "5705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,327,404,469,534,606,678,755,830,896,969,1043,1116,1193,1269,1341,1411,1480,1562,1630,1701,1768", "endColumns": "65,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "116,188,255,322,399,464,529,601,673,750,825,891,964,1038,1111,1188,1264,1336,1406,1475,1557,1625,1696,1763,1835"}, "to": {"startLines": "50,64,146,153,154,158,171,172,173,225,226,229,237,240,241,242,244,245,247,249,250,252,255,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3391,4622,10939,11325,11392,11680,12550,12615,12687,16416,16493,16717,17093,17313,17387,17460,17613,17689,17835,17975,18044,18227,18433,18618,18685", "endColumns": "65,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "3452,4689,11001,11387,11464,11740,12610,12682,12754,16488,16563,16778,17161,17382,17455,17532,17684,17756,17900,18039,18121,18290,18499,18680,18752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "52,53,54,55,56,57,58,251", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3526,3618,3718,3812,3909,4005,4103,18126", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3613,3713,3807,3904,4000,4098,4198,18222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,141,234,337", "endColumns": "85,92,102,93", "endOffsets": "136,229,332,426"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "6641,10649,10742,10845", "endColumns": "85,92,102,93", "endOffsets": "6722,10737,10840,10934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,612,686,759,830,911,989,1048,1109,1186,1262,1326,1387,1446,1511,1599,1687,1776,1840,1909,1974,2032,2109,2185,2246,2311,2364,2421,2467,2526,2582,2644,2701,2761,2817,2873,2937,3000,3064,3114,3170,3240,3310", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "282,449,607,681,754,825,906,984,1043,1104,1181,1257,1321,1382,1441,1506,1594,1682,1771,1835,1904,1969,2027,2104,2180,2241,2306,2359,2416,2462,2521,2577,2639,2696,2756,2812,2868,2932,2995,3059,3109,3165,3235,3305,3358"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,504,7228,7302,7375,7446,7527,7605,7664,7725,7802,7878,7942,8003,8062,8127,8215,8303,8392,8456,8525,8590,8648,8725,8801,8862,9516,9569,9626,9672,9731,9787,9849,9906,9966,10022,10078,10142,10205,10269,10319,10375,10445,10515", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "332,499,657,7297,7370,7441,7522,7600,7659,7720,7797,7873,7937,7998,8057,8122,8210,8298,8387,8451,8520,8585,8643,8720,8796,8857,8922,9564,9621,9667,9726,9782,9844,9901,9961,10017,10073,10137,10200,10264,10314,10370,10440,10510,10563"}}]}]}